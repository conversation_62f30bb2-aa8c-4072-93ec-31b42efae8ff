<?php $__env->startSection('TitlePage', 'Coupons'); ?>
<?php $__env->startSection('content'); ?>

<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>Coupon List</h4>
            <h6>Manage your discount coupons</h6>
        </div>
        <div class="page-btn">
            <a href="<?php echo e(route('coupons.create')); ?>" class="btn btn-added">
                <img src="<?php echo e(asset('admin/assets/img/icons/plus.svg')); ?>" alt="img" class="me-1">Add Coupon
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table datatable">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Code</th>
                            <th>Name</th>
                            <th>Discount</th>
                            <th>Type</th>
                            <th>Active</th>
                            <th>Start</th>
                            <th>End</th>
                            <th>Created By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $coupons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $coupon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($key + 1); ?></td>
                            <td><?php echo e($coupon->code); ?></td>
                            <td><?php echo e($coupon->name ?? '-'); ?></td>
                            <td>
                                <?php if($coupon->discount_type == 'percentage'): ?>
                                    <?php echo e($coupon->discount_amount); ?> %
                                <?php else: ?>
                                    <?php echo e($coupon->discount_amount); ?> EGP
                                <?php endif; ?>
                            </td>
                            <td><?php echo e(ucfirst($coupon->discount_type)); ?></td>
                            <td>
                                <?php if($coupon->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($coupon->start_date ?? '-'); ?></td>
                            <td><?php echo e($coupon->end_date ?? '-'); ?></td>
                            <td><?php echo e($coupon->creator->name ?? '-'); ?></td>
                            <td>
                                <a href="<?php echo e(route('coupons.edit', $coupon->id)); ?>" class="btn btn-sm btn-warning">Edit</a>
                                <form action="<?php echo e(route('coupons.destroy', $coupon->id)); ?>" method="POST" style="display:inline;">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" onclick="return confirm('Are you sure?')" class="btn btn-sm btn-danger">
                                        Delete
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/coupon/index.blade.php ENDPATH**/ ?>