/* Multilingual Form Styles */

/* Arabic input styling */
input[dir="rtl"],
textarea[dir="rtl"] {
    text-align: right;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Language labels */
.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

/* Language indicators */
.lang-indicator {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 8px;
}

.lang-indicator.en {
    background-color: #e3f2fd;
    color: #1976d2;
}

.lang-indicator.ar {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

/* Form sections */
.multilingual-section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background-color: #fafafa;
}

.multilingual-section h5 {
    color: #333;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* Input focus states */
input[name*="_en"]:focus,
textarea[name*="_en"]:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
}

input[name*="_ar"]:focus,
textarea[name*="_ar"]:focus {
    border-color: #7b1fa2;
    box-shadow: 0 0 0 0.2rem rgba(123, 31, 162, 0.25);
}

/* Table styling for multilingual content */
.multilingual-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.multilingual-table td[dir="rtl"] {
    text-align: right;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Badge styling */
.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Code styling for slugs */
code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.875em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .multilingual-section {
        padding: 15px;
    }

    .col-lg-6 {
        margin-bottom: 15px;
    }
}

/* Form validation styling */
.is-invalid {
    border-color: #dc3545;
}

.text-danger {
    color: #dc3545 !important;
    font-size: 0.875em;
    margin-top: 5px;
}

/* Success messages */
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Improved spacing */
.form-group {
    margin-bottom: 1.5rem;
}

.card-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

/* Enhanced Table Styling */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transition: background-color 0.15s ease-in-out;
}

.table-dark th {
    background-color: #343a40 !important;
    border-color: #454d55 !important;
    color: #fff !important;
}

/* Enhanced Badges */
.badge.rounded-pill {
    padding: 0.5em 0.75em;
    font-size: 0.75em;
}

/* Button Groups */
.btn-group .btn {
    margin: 0 2px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Stats Cards */
.card.bg-primary, .card.bg-success, .card.bg-warning, .card.bg-danger {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card.bg-primary:hover, .card.bg-success:hover, .card.bg-warning:hover, .card.bg-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Pagination Styling */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #007bff;
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
}

.page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* Image Styling */
.img-thumbnail {
    border: 2px solid #dee2e6;
    transition: border-color 0.15s ease-in-out;
}

.img-thumbnail:hover {
    border-color: #007bff;
}

/* Empty State Styling */
.text-muted i.fa-3x {
    opacity: 0.3;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin: 2px 0;
        width: 100%;
    }

    .card.bg-primary, .card.bg-success, .card.bg-warning, .card.bg-danger {
        margin-bottom: 1rem;
    }
}
