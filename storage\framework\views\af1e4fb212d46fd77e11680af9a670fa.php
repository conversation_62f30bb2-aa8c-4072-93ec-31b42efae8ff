<div class="container-fluid pt-5 pb-3">
    <h2 class="section-title position-relative text-uppercase mx-xl-5 mb-4" data-aos="fade-down">
        <span class="bg-secondary pr-3"><?php echo e(__('home.featured_products')); ?></span>
    </h2>
    <div class="row px-xl-5" id="featured-products">
        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-lg-3 col-md-4 col-sm-6 pb-1" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
            <div class="product-item bg-light mb-4">
                <div class="product-img position-relative overflow-hidden">
                    <?php
                    $image = $product->images->first();
                    ?>
                    <?php if($image && $image->image_path): ?>
                        <img src="<?php echo e(Storage::url($image->image_path)); ?>" alt="<?php echo e($product->name); ?>"
                        class="img-fluid image-Custom" style="width: 100%; height: 250px; object-fit: contain;">
                    <?php else: ?>
                        <img src="<?php echo e(asset('admin/assets/img/product/noimage.png')); ?>" alt="<?php echo e($product->name); ?>"
                        class="img-fluid image-Custom" style="width: 100%; height: 250px; object-fit: contain;">
                    <?php endif; ?>

                    <?php if($product->selling_price < $product->price): ?>
                    <div class="badge bg-danger text-white position-absolute" style="top: 10px; left: 10px; z-index: 1; padding: 5px 10px; border-radius: 3px;">
                        <?php echo e(round((($product->price - $product->selling_price) / $product->price) * 100)); ?>% <?php echo e(__('product.discount')); ?>

                    </div>
                    <?php endif; ?>

                    <div class="product-action">
                        <?php if($product->qty >= $product->minqty): ?>
                            <a class="btn btn-outline-dark btn-square add-to-cart" data-toggle="tooltip" title="<?php echo e(__('product.add_to_cart')); ?>"
                            data-product-id="<?php echo e($product->id); ?>"
                            href="javascript:void(0);">
                            <i class="fa fa-cart-plus"></i></a>
                        <?php else: ?>
                            <a class="btn btn-outline-dark btn-square" data-toggle="tooltip" title="<?php echo e(__('product.unavailable')); ?>"><i class="fa-solid fa-store-slash"></i></a>
                        <?php endif; ?>
                        <a class="btn btn-outline-dark btn-square" onclick="addToWishlist(<?php echo e($product->id); ?>)" href="javascript:void(0);" data-toggle="tooltip" title="<?php echo e(__('product.add_wishlist')); ?>"><i class="far fa-heart"></i></a>
                        <a class="btn btn-outline-dark btn-square" href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" data-toggle="tooltip" title="<?php echo e(__('product.view_deatils')); ?>"><i class="fa-solid fa-eye"></i></a>
                    </div>
                </div>
                <div class="text-center py-4">
                    <a class="h6 text-decoration-none" href="<?php echo e(route('get_product_slug',[$product->category->slug,$product->slug])); ?>" style="display: block; height: 40px; overflow: hidden;"><?php echo e($product->name); ?></a>
                    <div class="d-flex align-items-center justify-content-center mt-1">
                        <span class="text-muted small">
                            <a href="<?php echo e(route('website.category_slug', $product->category->slug)); ?>" class="text-muted"><?php echo e($product->category->name); ?></a>
                            <?php if($product->brand): ?>
                             | <a href="<?php echo e(route('website.shop')); ?>?brand=<?php echo e($product->brand_id); ?>" class="text-muted"><?php echo e($product->brand->name); ?></a>
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="d-flex align-items-center justify-content-center mt-2">
                        <h5><?php echo e($product->selling_price); ?> <?php echo e(__('product.egp')); ?></h5>
                        <?php if($product->selling_price < $product->price): ?>
                        <h6 class="text-muted ml-2"><del><?php echo e($product->price); ?> <?php echo e(__('product.egp')); ?></del></h6>
                        <?php endif; ?>
                    </div>
                    <div class="d-flex align-items-center justify-content-center mb-1">
                        <div class="back-stars">
                            <small class="fa fa-star"></small>
                            <small class="fa fa-star"></small>
                            <small class="fa fa-star"></small>
                            <small class="fa fa-star"></small>
                            <small class="fa fa-star"></small>
                            <div class="front-stars" style="width: <?php echo e($product->avgRatingPer); ?>%">
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                            </div>
                        </div>
                        <small class="pt-1"> (<?php echo e($product->product_ratings_count); ?>)</small>
                    </div>
                    <div class="d-flex align-items-center justify-content-center mt-3">
                        <a href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" class="btn btn-primary"><?php echo e(__('main.show_details')); ?> <i class="fa-solid fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </div>
</div>


<?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/website/sections/trend-products.blade.php ENDPATH**/ ?>