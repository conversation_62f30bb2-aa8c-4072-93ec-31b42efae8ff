<?php $__env->startSection('TitlePage', 'categories'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>Product Category list</h4>
            <h6>View/Search product Category</h6>
        </div>
        <div class="page-btn">
            <a href="<?php echo e(route('categories.create')); ?>" class="btn btn-added">
                <img src="<?php echo e(asset('admin/assets/img/icons/plus.svg')); ?>" class="me-1" alt="img">Add Category
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-top">
                <div class="search-set">
                    <div class="search-input">
                        <a class="btn btn-searchset"><img src="<?php echo e(asset('admin/assets/img/icons/search-white.svg')); ?>"
                                alt="img"></a>
                    </div>
                </div>
                <div class="wordset">
                    <ul>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/pdf.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/excel.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/printer.svg')); ?>" alt="img"></a>
                        </li>
                    </ul>
                </div>
            </div>



            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th width="5%">#</th>
                            <th width="10%"><i class="fas fa-image me-1"></i> Image</th>
                            <th width="20%"><i class="fas fa-flag me-1"></i> Name (EN)</th>
                            <th width="20%"><i class="fas fa-flag me-1"></i> Name (AR)</th>
                            <th width="10%"><i class="fas fa-eye me-1"></i> Showing</th>
                            <th width="10%"><i class="fas fa-star me-1"></i> Popular</th>
                            <th width="15%"><i class="fas fa-link me-1"></i> Slug</th>
                            <th width="10%"><i class="fas fa-cogs me-1"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="align-middle">
                            <td class="text-center">
                                <span class="badge bg-primary"><?php echo e(($categories->currentPage() - 1) * $categories->perPage() + $loop->iteration); ?></span>
                            </td>
                            <td class="text-center">
                                <img width="60" height="60" src="<?php echo e(Storage::url($category->image)); ?>"
                                     alt="<?php echo e($category->getTranslation('name', 'en')); ?>"
                                     class="img-thumbnail rounded-circle shadow-sm">
                            </td>
                            <td>
                                <div class="fw-bold text-primary"><?php echo e($category->getTranslation('name', 'en') ?? 'N/A'); ?></div>
                                <small class="text-muted"><?php echo e(Str::limit($category->getTranslation('description', 'en'), 50)); ?></small>
                            </td>
                            <td dir="rtl">
                                <div class="fw-bold text-success"><?php echo e($category->getTranslation('name', 'ar') ?? 'غير متوفر'); ?></div>
                                <small class="text-muted"><?php echo e(Str::limit($category->getTranslation('description', 'ar'), 50)); ?></small>
                            </td>
                            <td class="text-center">
                                <?php if($category->is_showing == 1): ?>
                                <span class="badge bg-success rounded-pill">
                                    <i class="fas fa-eye me-1"></i> Visible
                                </span>
                                <?php else: ?>
                                <span class="badge bg-danger rounded-pill">
                                    <i class="fas fa-eye-slash me-1"></i> Hidden
                                </span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <?php if($category->is_popular == 1): ?>
                                <span class="badge bg-warning text-dark rounded-pill">
                                    <i class="fas fa-star me-1"></i> Popular
                                </span>
                                <?php else: ?>
                                <span class="badge bg-secondary rounded-pill">
                                    <i class="fas fa-minus me-1"></i> Normal
                                </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <code class="bg-light p-2 rounded"><?php echo e($category->slug); ?></code>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('categories.show',$category->id)); ?>"
                                       class="btn btn-sm btn-outline-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('categories.edit',$category->id)); ?>"
                                       class="btn btn-sm btn-outline-primary" title="Edit Category">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo e($category->id); ?>" title="Delete Category">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <?php echo $__env->make('admin.categories.delete_modal',['type'=>'category','data'=>$category,'routes'=>'categories.destroy'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="fas fa-folder-open fa-3x mb-3"></i>
                                    <h5>No Categories Found</h5>
                                    <p>Start by creating your first category</p>
                                    <a href="<?php echo e(route('categories.create')); ?>" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i> Add New Category
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination and Statistics -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <span class="text-muted">
                            Showing <?php echo e($categories->firstItem() ?? 0); ?> to <?php echo e($categories->lastItem() ?? 0); ?>

                            of <?php echo e($categories->total()); ?> categories
                        </span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end">
                        <?php echo e($categories->links('pagination::bootstrap-4')); ?>

                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-list fa-2x mb-2"></i>
                            <h4><?php echo e($categories->total()); ?></h4>
                            <small>Total Categories</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-eye fa-2x mb-2"></i>
                            <h4><?php echo e($categories->where('is_showing', 1)->count()); ?></h4>
                            <small>Visible Categories</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-star fa-2x mb-2"></i>
                            <h4><?php echo e($categories->where('is_popular', 1)->count()); ?></h4>
                            <small>Popular Categories</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <i class="fas fa-eye-slash fa-2x mb-2"></i>
                            <h4><?php echo e($categories->where('is_showing', 0)->count()); ?></h4>
                            <small>Hidden Categories</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/categories/index.blade.php ENDPATH**/ ?>