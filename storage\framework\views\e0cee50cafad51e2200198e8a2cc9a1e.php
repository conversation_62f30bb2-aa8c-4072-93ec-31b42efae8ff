<?php $__env->startSection('TitlePage', 'Dashbaoard'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="row">

        <div class="row align-items-center">
            <div class="col-lg-3 col-sm-6 col-12">
                <div class="dash-widget dash1">
                    <div class="dash-widgetimg">
                        <span><img src="<?php echo e(asset('admin/assets/img/icons/dash2.svg')); ?>" alt="img"></span>
                    </div>
                    <div class="dash-widgetcontent">
                        <h5><span class="counters" data-count="<?php echo e($totalRevenue); ?>">$<?php echo e($totalRevenue); ?></span> L.E</h5>
                        <h6>Total Revenue (EGP)</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-sm-6 col-12">
                <div class="dash-widget">
                    <div class="dash-widgetimg">
                        <span><img src="<?php echo e(asset('admin/assets/img/icons/dash1.svg')); ?>" alt="img"></span>
                    </div>
                    <div class="dash-widgetcontent">
                        <h5><span class="counters" data-count="<?php echo e($revenueThisMonth); ?>">$<?php echo e($revenueThisMonth); ?></span> L.E</h5>
                        <h6>Revenue This Monthly</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-sm-6 col-12">
                <div class="dash-widget dash3">
                    <div class="dash-widgetimg">
                        <span><img src="<?php echo e(asset('admin/assets/img/icons/dash4.svg')); ?>" alt="img"></span>
                    </div>
                    <div class="dash-widgetcontent">
                        <h5><span class="counters" data-count="<?php echo e($shippingThisMonth); ?>">$<?php echo e($shippingThisMonth); ?></span> L.E</h5>
                        <h6>Shipping Cost This Monthly</h6>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-sm-6 col-12">
                <div class="dash-widget dash2">
                    <div class="dash-widgetimg">
                        <span><img src="<?php echo e(asset('admin/assets/img/icons/dash3.svg')); ?>" alt="img"></span>
                    </div>
                    <div class="dash-widgetcontent">
                        <h5><span class="counters" data-count="<?php echo e($last30DaysSale); ?>">$<?php echo e($last30DaysSale); ?></span> L.E</h5>
                        <h6>Revenue Last 30 Days (EGP)</h6>
                        <h5><span class="counters" data-count="<?php echo e($revenueLastMonth); ?>">$<?php echo e($revenueLastMonth); ?></span> L.E</h5>
                        <h6>Revenue Last Monthly (<?php echo e($lastMonthStartname); ?>)</h6>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-sm-6 col-12 d-flex">
                <div class="dash-count">
                    <div class="dash-counts">
                        <h4><?php echo e($products); ?></h4>
                        <h5>Total Products</h5>
                    </div>
                    <div class="dash-imgs">
                        <i data-feather="package"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-sm-6 col-12 d-flex">
                <div class="dash-count das1">
                    <div class="dash-counts">
                        <h4><?php echo e($totalCustomers); ?></h4>
                        <h5>Total Customers</h5>
                    </div>
                    <div class="dash-imgs">
                        <i data-feather="user-check"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-sm-6 col-12 d-flex">
                <div class="dash-count das2">
                    <div class="dash-counts">
                        <h4><?php echo e($totalOrders); ?></h4>
                        <h5>Total Orders</h5>
                    </div>
                    <div class="dash-imgs">
                        <i data-feather="file-text"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-sm-6 col-12 d-flex">
                <div class="dash-count das3">
                    <div class="dash-counts">
                        <h4><?php echo e($totalDelivered); ?></h4>
                        <h5>Total Delivered</h5>
                    </div>
                    <div class="dash-imgs">
                        <i data-feather="shopping-cart"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-sm-6 col-12 d-flex">
                <div class="dash-count">
                    <div class="dash-counts">
                        <h4><?php echo e($totalMessages); ?></h4>
                        <h5>Messages</h5>
                    </div>
                    <div class="dash-imgs">
                        <i data-feather="inbox"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-sm-6 col-12 d-flex">
                <div class="dash-count das1">
                    <div class="dash-counts">
                        <h4><?php echo e($totalPages); ?></h4>
                        <h5>Pages</h5>
                    </div>
                    <div class="dash-imgs">
                        <i data-feather="hash"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-7 col-sm-12 col-12 d-flex">
            <div class="card flex-fill">
                <div class="card-header pb-0 d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Recent Orders</h4>
                    <a href="<?php echo e(route('orders.index')); ?>" class="btn-sm btn-primary">
                        See More Orders
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table datanew ">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="checkboxs">
                                            <input type="checkbox" id="select-all">
                                            <span class="checkmarks"></span>
                                        </label>
                                    </th>
                                    <th>#</th>
                                    <th>Customer Name</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Payment</th>
                                    <th>Total</th>
                                    <th>Shipped date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $lastOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <label class="checkboxs">
                                                <input type="checkbox">
                                                <span class="checkmarks"></span>
                                            </label>
                                        </td>
                                        <td><a href="<?php echo e(route('order.detail', ['orderId' => $order->id])); ?>"><?php echo e($order->id); ?></a></td>
                                        <td><a href="<?php echo e(route('order.detail', ['orderId' => $order->id])); ?>"><?php echo e($order->name); ?></a></td>
                                        <td><?php echo e(\Carbon\Carbon::parse($order->created_at)->format('d M, Y - D')); ?></td>

                                        <td>
                                            <?php if($order->status == 'delivered'): ?>
                                                <span class="badges bg-lightgreen">Delivered</span>
                                            <?php elseif($order->status == 'shipped'): ?>
                                                <span class="badges bg-lightyellow">Shipped</span>
                                            <?php elseif($order->status == 'pending'): ?>
                                                <span class="badges bg-primary">Pending</span>
                                            <?php else: ?>
                                                <span class="badges bg-lightred fw-bold">Cancelled</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($order->payment_status == 'paid'): ?>
                                                <span class="badges bg-lightgreen">Paid</span>
                                            <?php else: ?>
                                                <span class="badges bg-lightred">Not Paid</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($order->grand_total); ?> EGP</td>
                                        <td><?php echo e($order->shipped_date ? $order->shipped_date : 'N/A'); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="11">No records found</td>
                                    </tr>
                                <?php endif; ?>

                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
        <div class="col-lg-5 col-sm-12 col-12 d-flex">
            <div class="card flex-fill">
                <div class="card-header pb-0 d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Top 10 Products</h4>
                    <div class="dropdown">
                        <a href="javascript:void(0);" data-bs-toggle="dropdown" aria-expanded="false" class="dropset">
                            <i class="fa fa-ellipsis-v"></i>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <li><a href="productlist.html" class="dropdown-item">Product List</a></li>
                            <li><a href="addproduct.html" class="dropdown-item">Product Add</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive dataview">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th>Sno</th>
                                    <th>Products</th>
                                    <th>Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $top10Products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($index + 1); ?></td>
                                        <td class="productimgname">
                                            <a href="productlist.html"><?php echo e($product->name); ?></a>
                                        </td>
                                        <td>$<?php echo e(number_format($product->price, 2)); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-0">
        <div class="card-body">
            <h4 class="card-title">Recent Orders</h4>
            <div class="table-responsive dataview">
                <table class="table datatable">
                    <thead>
                        <tr>
                            <th>Sno</th>
                            <th>Customer Name</th>
                            <th>Total Spent</th>
                            <th>Last Order Date</th>
                            <th>Last Order Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $top10Customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($index + 1); ?></td>
                                <td><?php echo e($customer->name); ?></td>
                                <td>$<?php echo e(number_format($customer->total_spent, 2)); ?></td>
                                <td>
                                    <?php if($customer->last_order_date): ?>
                                        <?php echo e(\Carbon\Carbon::parse($customer->last_order_date)->format('Y-m-d H:i:s')); ?>

                                    <?php else: ?>
                                        N/A
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($customer->last_order_status); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/index.blade.php ENDPATH**/ ?>