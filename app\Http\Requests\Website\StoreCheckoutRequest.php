<?php

namespace App\Http\Requests\Website;

use Illuminate\Foundation\Http\FormRequest;

class StoreCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' =>'required|string|min:5|max:255',
            'email' =>'required|string|email|max:255',
            'phone' =>'required',
            'address' =>'required|min:15',
            'country' =>'required',
            'city' =>'required',
            'state' =>'required',
            'zip' =>'required',
        ];
    }
}
