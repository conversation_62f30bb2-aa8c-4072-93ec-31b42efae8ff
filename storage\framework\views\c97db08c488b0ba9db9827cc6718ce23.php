<?php $__env->startSection('TitlePage', __('login.login')); ?>
<?php $__env->startSection('content'); ?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-7 col-lg-6">
            <div class="shadow-sm p-5 bg-body rounded">
                <?php if(session('status')): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo e(session('status')); ?>

                    </div>
                <?php endif; ?>

                <form action="<?php echo e(route('login.action')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <h4 class="modal-title pb-3"><?php echo e(__('login.welcome_back')); ?></h4>
                    <p class="text-muted mb-4"><?php echo e(__('login.sign_in_to_continue')); ?></p>

                    <div class="form-group">
                        <label for="email"><?php echo e(__('login.email')); ?></label>
                        <input type="email" id="email" name="email" class="form-control" placeholder="<?php echo e(__('login.email')); ?>" value="<?php echo e(old('email')); ?>">
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-group">
                        <label for="password"><?php echo e(__('login.password')); ?></label>
                        <input type="password" id="password" name="password" class="form-control" placeholder="<?php echo e(__('login.password')); ?>">
                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                        <label class="form-check-label" for="remember"><?php echo e(__('login.remember_me')); ?></label>
                    </div>

                    <div class="form-group mb-4">
                        <a href="<?php echo e(route('password.request')); ?>" class="forgot-link btn-link"><?php echo e(__('login.forgot_password')); ?></a>
                    </div>

                    <button type="submit" class="btn btn-dark btn-block btn-lg"><?php echo e(__('login.login')); ?></button>
                </form>

                <div class="text-center mt-3">
                    <?php echo e(__('login.dont_have_account')); ?> <a href="<?php echo e(route('register')); ?>" class="fw-bold hover-a"><?php echo e(__('login.create_account')); ?></a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/auth/login.blade.php ENDPATH**/ ?>