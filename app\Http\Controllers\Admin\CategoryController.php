<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreCategoryRequest;
use App\Http\Requests\Admin\UpdateCategoryRequest;
use Illuminate\Support\Facades\Storage;
use App\Models\Category;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = Category::paginate(10);
        return view('admin.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCategoryRequest $request)
    {
        $validated = $request->validated();

        Category::create([
            'name' => [
                'en' => $request->name_en,
                'ar' => $request->name_ar
            ],
            'slug' => $request->slug,
            'description' => [
                'en' => $request->description_en,
                'ar' => $request->description_ar
            ],
            'is_showing' => $request->is_showing ? '1' : '0',
            'is_popular' => $request->is_popular ? '1' : '0',
            'image' => $request->file('image')->store('public/categories'),
            'meta_title' => [
                'en' => $request->meta_title_en,
                'ar' => $request->meta_title_ar
            ],
            'meta_description' => [
                'en' => $request->meta_description_en,
                'ar' => $request->meta_description_ar
            ],
            'meta_keywords' => [
                'en' => $request->meta_keywords_en,
                'ar' => $request->meta_keywords_ar
            ],
        ]);

        flash()->success('Category added successfully', 'Success', ['timeOut' => 3000]);

        return redirect()->route('categories.index');
    }



    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $category = Category::findOrFail($id);
        return view('admin.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        return view('admin.categories.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCategoryRequest $request, string $id)
    {
        $category = Category::find($id);
        if ($request->hasFile('image')) {
            Storage::delete($category->image);
            $image = $request->file('image')->store('public/categories');
            $category->image = $image;
        }
        $category->update([
            'name' => $request->name,
            'slug' => $request->slug,
            'description' => $request->description,
            'is_showing' => $request->is_showing ? '1' : '0',
            'is_popular' => $request->is_popular ? '1' : '0',
            'image' => $category->image,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
        ]);

        flash()->success('Update category is done ', 'Success Update', ['timeOut' => 20000]);
        return redirect()->route('categories.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        Category::where("id", $id)->delete();
        return redirect()->route('categories.index');
    }
}
